import { datacenterHttp, defHttp } from '@/utils/http/axios';
import { LoginParams, GetUserInfoModel } from './model/userModel';
import { RouteItem } from './model/menuModel';
import { ErrorMessageMode } from '#/axios';
import { LoginInfo } from '#/store';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  Login = '/sysAuthAbout/login',
  Logout = '/sysAuthAbout/logoutCurrent',
  GetUserInfo = '/sysAuthAbout/getAccountDetail',
  GetPermCode = '/sysAuthAbout/getMenuListCurrent',
  TestRetry = '/testRetry',
  GetAccountInfoList = '/sysAuthAbout/getAccountInfoList',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return datacenterHttp.post<LoginInfo>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo(params) {
  return datacenterHttp.get<GetUserInfoModel>(
    {
      url: Api.GetUserInfo,
      params,
    },
    { errorMessageMode: 'none' }
  );
}

export function getPermCode() {
  return datacenterHttp.get<RouteItem[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return datacenterHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    }
  );
}

export function getAccountInfoList(params) {
  return datacenterHttp.get<GetUserInfoModel>(
    {
      url: Api.GetAccountInfoList,
      params,
    },
    { isTransformResponse: false }
  );
}

// 修改密码
export const changePassword = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: '/sysAuthAbout/updatePwd',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
