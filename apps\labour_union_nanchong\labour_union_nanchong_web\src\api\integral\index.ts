import { datacenterHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum IntegralApplication {
  //分页查询
  findList = '/findIntegralRuleManageVoList',
  //新增规则
  saveOrUpdate = '/updateIntegralRuleManageById',
  //修改规则
  upDate = '/updateIntegralRuleManageById',
  //删除:目前没接口用不上
  /* delete = '/deleteConvenienceApplication',
  getDetails = '/queryConvenienceApplicationById',*/
}

function getApi(url?: string) {
  if (!url) {
    return '/customIntegral';
  }
  return '/customIntegral' + url;
}

//列表
export const list = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(IntegralApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(IntegralApplication.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const update = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(IntegralApplication.upDate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
/*export const deleteIntegralApplication = (ids: number[]) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(IntegralApplication.delete),
      params: {
        ids,
      },
    },
    {
      isTransformResponse: false,
    },
  );
};*/

//详情
/*export const getDetails = (params?: IntegralApplicationItem) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(IntegralApplication.getDetails), params },
    {
      isTransformResponse: false,
    },
  );
};*/
