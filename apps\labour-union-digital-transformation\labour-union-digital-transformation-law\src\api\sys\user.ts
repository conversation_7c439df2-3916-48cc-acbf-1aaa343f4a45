import { datacenterHttp, defHttp } from '@/utils/http/axios';
import { LoginParams, GetUserInfoModel } from './model/userModel';
import { RouteItem } from './model/menuModel';
import { ErrorMessageMode } from '#/axios';

enum Api {
  Login = '/sysCommon/unionManageLoginSim',
  Logout = '/dataCenterBusiness/logout',
  GetUserInfo = '/sysCommon/unionManageLoginSim',
  GetPermCode = '/dataCenterBusiness/getCurrentMenuList',
  TestRetry = '/testRetry',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<string>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo(token) {
  return defHttp.get<GetUserInfoModel>(
    {
      url: Api.GetUserInfo,
      params: {
        token,
      },
    },
    { errorMessageMode: 'none' }
  );
}

export function getPermCode() {
  return datacenterHttp.get<RouteItem[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return datacenterHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    }
  );
}
