import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp, defHttp, h5Http } from '@/utils/http/axios';

enum News {
  findList = '/findList',
  countByCategoryId = '/countByCategoryId',
  getMaxSortNumberByCategoryId = '/getMaxSortNumberByCategoryId',
  getStayAuditNewsList = '/getStayAuditNewsList',
  heating = '/setHeating',
  release = '/release',
  audit = '/batchAudit',
  batchAudit = '/batchAudit',
  getOneNews = '/getOneNews',
  getNewsMainTable = '/getNewsMainTable',
  setTop = '/setTop',
  newsRegeneration = '/newsRegeneration',
  setNewsSort = '/setNewsSort',
  generatePreviewLink = '/generatePreviewLink',
  getKeyPair = '/encryptionUserinfo/getKeyPair',
  deleteKey = '/encryptionUserinfo',
  logicallyDelete = '/logicallyDelete',
  saveOrUpdateNewsSetUp = '/saveOrUpdateNewsSetUp',
  getReferToNews = '/getReferToNews',
  newsExtractKeywords = '/newsExtractKeywords',
  getNewsAuditRecord = '/getNewsAuditRecord',
}

function getApi(url?: string) {
  if (!url) {
    return '/h5NewsInfo';
  }
  return '/h5NewsInfo' + url;
}

//列表
export const getNewsList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 根据栏目业务id查询栏目下新闻个数
export const countByCategoryId = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.countByCategoryId), params },
    {
      isTransformResponse: false,
    }
  );
};

// 根据栏目id查询最大排序号
export const getMaxSortNumberByCategoryId = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.getMaxSortNumberByCategoryId), params },
    {
      isTransformResponse: false,
    }
  );
};

// 排序号排序方式查询参照新闻列表
export const getReferToNews = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.getReferToNews), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const newsAdd = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

// 提取关键字
export const newsExtractKeywords = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.newsExtractKeywords), params },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const newsUpdate = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const newsDelete = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.logicallyDelete), params },
    {
      isTransformResponse: false,
    }
  );
};

// 加热
export const newsHeating = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.heating), params },
    {
      isTransformResponse: false,
    }
  );
};

// 发布
export const newsRelease = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.release), params },
    {
      isTransformResponse: false,
    }
  );
};

//单个审核
export const newsAudit = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.audit), params },
    {
      isTransformResponse: false,
    }
  );
};

//批量审核
export const newsBatchAudit = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.batchAudit), params },
    {
      isTransformResponse: false,
    }
  );
};

//列表
export const newsAuditList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.getStayAuditNewsList), params },
    {
      isTransformResponse: false,
    }
  );
};

//单个详情(包含副表数据, app, 官网, 等展示端口数据)
export const newsGetOneNews = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.getOneNews), params },
    {
      isTransformResponse: false,
    }
  );
};

//单个详情(只有新闻主表信息)
export const getNewsMainTable = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(News.getNewsMainTable), params },
    {
      isTransformResponse: false,
    }
  );
};

//重新生成数字播报
export const newsRegeneration = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.newsRegeneration), params },
    {
      isTransformResponse: false,
    }
  );
};

//单个置顶
export const newsTop = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.setTop), params },
    {
      isTransformResponse: false,
    }
  );
};
// 单个设置排序
export const setNewsSort = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.setNewsSort), params },
    {
      isTransformResponse: false,
    }
  );
};

// 获取新闻预览链接
export const generatePreviewLink = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(News.generatePreviewLink), params },
    {
      isTransformResponse: false,
    }
  );
};

// 点赞
export const newsLike = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: '/customFlowRecord/getStatefulListBySourceId', params },
    {
      isTransformResponse: false,
    }
  );
};

// 收藏
export const newsCollection = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: '/customFlowRecord/getStatefulListBySourceId', params },
    {
      isTransformResponse: false,
    }
  );
};

// 统计指标明细查询
export const getStatisticalBySourceIdAndKey = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: '/customFlowRecord/getStatisticalBySourceIdAndKey', params },
    {
      isTransformResponse: false,
    }
  );
};

// getkey
export const getKeyPair = () => {
  return defHttp.get<BasicResponse>({ url: News.getKeyPair });
};

// delete
export const deleteKey = params => {
  return defHttp.delete<BasicResponse>({ url: News.deleteKey, params });
};

// 获取新闻审核记录
export const NewsAuditRecord = params => {
  return h5Http.get<Recordable[]>({
    url: getApi(News.getNewsAuditRecord),
    params,
  });
};
