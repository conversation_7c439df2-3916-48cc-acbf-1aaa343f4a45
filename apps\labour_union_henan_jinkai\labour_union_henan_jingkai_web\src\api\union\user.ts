import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum API {
  view = '/getMemberInfoByMemberId',
  saveApi = '/addMemberInfo',
  updateApi = '/updateMemberInfo',
}

function getApi(url?: string) {
  if (!url) {
    return '/member';
  }
  return '/member' + url;
}

// 新增
export const saveApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
