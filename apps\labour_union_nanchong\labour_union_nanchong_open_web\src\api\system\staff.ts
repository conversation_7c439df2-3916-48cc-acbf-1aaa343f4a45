import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp, openHttp } from '/@/utils/http/axios';

enum API {
  findList = '/simpleFindVoList',
  view = '/',
  saveOrUpdate = '/',
  saveApi = '/dataCenterBusiness/addOpenPlatformStaff',
  updateApi = '/dataCenterBusiness/updateOpenAccountAboutInfo',
  resetPassword = '/resetPassword',
  threeList = '/openCompanyInfo/findThreeList',
  updateAccountCompany = '/updateAccountCompany',
}

function getApi(url?: string) {
  if (!url) {
    return '/accountInfo';
  }
  return '/accountInfo' + url;
}

// 列表
export const list = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 树列表
export const threeList = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: API.threeList, params },
    {
      isTransformResponse: true,
    }
  );
};

//修改商户归属
export const updateAccountCompany = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.updateAccountCompany),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增修改
export const saveOrUpdate = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: API.saveApi,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: API.updateApi,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const roleRelation = (params: Recordable) => {
  return datacenterHttp.get<Recordable[]>({
    url: '/roleRelation/findVoList',
    params,
  });
};

// 重置密码
export const resetPassword = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.resetPassword),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
