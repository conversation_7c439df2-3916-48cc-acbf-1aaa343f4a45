import { datacenterHttp } from '@/utils/http/axios';

enum Cadre {
  paged = '/findCadreList', // 获取干部信息（分页）
  find = '/getCadreDetail', // 查询单个干部信息
  addCadre = '/addCadre', // 新增干部
  updateCadreInfo = '/updateCadreInfo', // 修改干部
  importCadreInfo = '/importCadreInfo', // 导入干部
  delCadreInfo = '/delCadreInfo', // 删除干部
}

function getApi(url?: string) {
  if (!url) {
    return '/cadre';
  }
  return '/cadre' + url;
}

export const paged = params => {
  return datacenterHttp.post(
    {
      url: `${getApi(Cadre.paged)}?pageSize=${params.pageSize}&pageNum=${params.pageNum}`,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const find = (params, isTransformResponse?: boolean) => {
  return datacenterHttp.get(
    {
      url: getApi(Cadre.find),
      params,
    },
    {
      isTransformResponse,
    }
  );
};

export const addCadre = (params, isTransformResponse?: boolean) => {
  return datacenterHttp.post(
    {
      url: getApi(Cadre.addCadre),
      params,
    },
    {
      isTransformResponse,
    }
  );
};

export const updateCadreInfo = (params, isTransformResponse?: boolean) => {
  return datacenterHttp.post(
    {
      url: getApi(Cadre.updateCadreInfo),
      params,
    },
    {
      isTransformResponse,
    }
  );
};

export const importCadreInfo = (params, isTransformResponse?: boolean) => {
  return datacenterHttp.post(
    {
      url: getApi(Cadre.importCadreInfo),
      params,
    },
    {
      isTransformResponse,
    }
  );
};

export const delCadreInfo = (params, isTransformResponse?: boolean) => {
  return datacenterHttp.post(
    {
      url: getApi(Cadre.delCadreInfo),
      params,
    },
    {
      isTransformResponse,
    }
  );
};
