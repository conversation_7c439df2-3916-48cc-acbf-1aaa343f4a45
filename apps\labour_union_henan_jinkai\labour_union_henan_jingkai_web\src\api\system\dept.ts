import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '@/utils/http/axios';

export function list(params: any) {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/sysAuthAbout/getDeptTreeList',
      params,
    },
    { isTransformResponse: false }
  );
}

export function getDeptList(params: any) {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/sysAuthAbout/getDeptList',
      params,
    },
    { isTransformResponse: false }
  );
}

export function getDeptIdListByAccount(params: any) {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/sysAuthAbout/getDeptIdListByAccount',
      params,
    },
    { isTransformResponse: false }
  );
}
export function getUserConfigByAccount(params: any) {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/sysAuthAbout/getUserConfigByAccount',
      params,
    },
    { isTransformResponse: false }
  );
}

export function saveByDTO(params: any) {
  return datacenterHttp.post<BasicResponse>(
    {
      url: '/sysAuthAbout/saveDeptInfo',
      params,
    },
    { isTransformResponse: false }
  );
}

export function updateDeptInfo(params: any) {
  return datacenterHttp.post<BasicResponse>(
    {
      url: '/sysAuthAbout/updateDeptInfo',
      params,
    },
    { isTransformResponse: false }
  );
}

export function deleteLine(params: number) {
  return datacenterHttp.post<BasicResponse>(
    {
      url: '/sysAuthAbout/deleteDeptById?deptId=' + params,
    },
    { isTransformResponse: false }
  );
}

//tree
export function getDeptInfoTreeList(params?: Recordable) {
  return datacenterHttp.post<Recordable[]>({
    url: '/sysAuthAbout/getDeptTreeList',
    params,
  });
}

//view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/sysAuthAbout/getDeptInfo',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
