<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/certificationAuditPengAn/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.auditStatus !== 'wait',
                onClick: handleAudit.bind(null, record),
                auth: '/certificationAuditPengAn/audit',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <WorkInfoModal
      @register="registerModal"
      :canFullscreen="false"
      width="60%"
    />

    <auditModal
      @register="auditRegisterModal"
      @success="auditHandleSuccess"
      :canFullscreen="false"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import WorkInfoModal from './../info/WorkInfoModal.vue';
import auditModal from './auditModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { auditModelWorkerCertification, auditRecordPengAn } from '@/api/workStar/pa';
import { view2 } from '/@/api/workStar/pa';
const { createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'modelWorkerId',
  columns: columns(),
  showIndexColumn: false,
  api: auditRecordPengAn,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 3,
    },
  },
  searchInfo: {
    modelType: 0,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/certificationAuditPengAn/audit', '/certificationAuditPengAn/view'],
  },
});

const [registerModal, { openModal }] = useModal();

const [auditRegisterModal, { openModal: auditopenModal, closeModal: auditCloseModal }] = useModal();

//详情
function handleView(record) {
  view2({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, {
      isUpdate: true,
      disabled: true,
      isCertification: true,
      record: data,
    });
  });
}

//单个审核
function handleAudit(record) {
  view2({ autoId: record.autoId }).then(({ data }) => {
    auditopenModal(true, {
      record: data,
    });
  });
}

function auditHandleSuccess({ values }) {
  auditModelWorkerCertification(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: '审核成功',
      });
      reload();
      auditCloseModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
</script>
