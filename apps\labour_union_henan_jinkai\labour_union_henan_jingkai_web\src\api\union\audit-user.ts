import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum API {
  authApplyList = '/authApplyList',
  view = '/',
  saveOrUpdate = '/',
  saveApi = '/',
  updateApi = '/',
  auditBatch = '/auditBatch',
}

function getApi(url?: string) {
  if (!url) {
    return '/member';
  }
  return '/member' + url;
}

// list
export const list = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: `${getApi(API.authApplyList)}?pageSize=${params.pageSize}&pageNum=${params.pageNum}`,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增修改
export const saveOrUpdate = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const auditBatch = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.auditBatch),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
