import { datacenterHttp, fileHttp } from '@/utils/http/axios';

enum Api {
  GetUserInfo = '/dataCenter/dataCenterBusiness/manageLogin',
  GetPermCode = '/dataCenter/dataCenterBusiness/getCurrentMenuList',
  Login = '/sysAuthAbout/login',
  code = '/sysCommon/generatorWebVerifyCode',
  // Logout = '/manageCommon/logout',
  // GetUserInfo = '/manageCommon/login',
  // GetPermCode = '/manageCommon/getCurrentMenuList',
  // Login = '/fkyLogin/manage',
}

export function generatorWebVerifyCode() {
  return datacenterHttp.get<Recordable>({ url: Api.code });
}

export const uploadFile = params => {
  return fileHttp.uploadFile(
    {
      url: '/minio/typeUploadFile',
    },
    {
      ...params,
    }
  );
};
