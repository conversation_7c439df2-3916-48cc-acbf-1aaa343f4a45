import { datacenterHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

export interface Dictionary {
  groupCode?: string;
  pageSize?: 0;
}

export interface DictionaryModal {
  autoId?: number;
  groupCode?: string;
  groupName?: string;
  dictCode?: string;
  dictName?: string;
  dictColor?: string;
  remark?: string;
}

export const queryDictionary = (params?: Dictionary) => {
  return datacenterHttp.get<DictionaryModal[]>({
    url: '/sysCommon/dictionaryList',
    params: {
      ...params,
      pageSize: 0,
    },
  });
};
