import { datacenterHttp, fileHttp, manageHttp, otherHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  GetUserInfo = '/dataCenter/dataCenterBusiness/manageLogin',
  GetPermCode = '/dataCenter/dataCenterBusiness/getCurrentMenuList',
  Login = '/sysAuthAbout/login',
  code = '/dataCenterBusiness/generatorWebVerifyCode',
  // Logout = '/manageCommon/logout',
  // GetUserInfo = '/manageCommon/login',
  // GetPermCode = '/manageCommon/getCurrentMenuList',
  // Login = '/fkyLogin/manage',
}

export function generatorWebVerifyCode() {
  return datacenterHttp.get<Recordable>({ url: Api.code });
}

export const uploadFile = params => {
  return fileHttp.uploadFile(
    {
      url: '/minio/uploadFile',
    },
    {
      ...params,
    }
  );
};

//获取下级区域
export const getArea = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/unionBasicData/areaNextLevel',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取union下级
export const unionUser = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/unionCustomData/customGetUser',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取union下级
export const unionNextLevel = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/unionBasicData/unionNextLevel',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//downUnion
export const downUnion = params => {
  return otherHttp.post<string>(
    {
      url: '/customExport/exportUnionInfo',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//工会
export const unionInfoList = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/unionCustomData/customGetUnion',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取当前工会信息
export const unionFind = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/unionBasicData/unionFind',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//down会员
export const downUser = params => {
  return otherHttp.post<string>(
    {
      url: '/customExport/exportAppUserInfo',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取前缀
export const getPrefix = () => {
  return fileHttp.get<Recordable>({
    url: '/minio/getVisitPrefix',
  });
};

export const userUnionCode = (params?: Recordable) => {
  return datacenterHttp.get({
    url: '/userCadreInfo/userUnionCode',
    params,
  });
};

//指标
export const currentCount = (params?: Recordable) => {
  return manageHttp.get({
    url: '/unionAndUserCount/kbByArea',
    params,
  });
};

//新增网站系统访问量,无返回值
export function asyncAddWebVisitSummary(params) {
  return datacenterHttp.get(
    {
      url: '/dataCenterBusiness/asyncAddWebVisitSummary',
      params,
    },
    { isReturnNativeResponse: true }
  );
}

//获取会员信息（分页，含下级）
export const userPagedAll = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: '/unionBasicData/userPagedAll',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
