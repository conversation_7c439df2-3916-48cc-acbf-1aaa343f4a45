import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp, h5Http } from '@/utils/http/axios';

enum modelWorker {
  findVoList = '/findVoList',
  updateLogicDeleteById = '/updateLogicDeleteById',
  exportFail = '/exportModelWorkerImportFail',
  importLines = '/getImportRecord',
  failGet = '/getImportFailByImportRecordId',
  failInfo = '/getImportFailByAutoId',
  exportModelWorker = '/importModelWorker',
  getImportTemplateURL = '/downloadDishTemplate',
  analysisIdCardNumber = '/analysisIdCardNumber',
  getMaxSortNumber = '/getMaxSortNumber',
  saveOrUpdateByDTO = '/saveOrUpdateByDTO',
  getVoByDtoPc = '/getVoByDtoPc',
}

function getApi(url?: string) {
  if (!url) {
    return '/modelWorkerInfo';
  }
  return '/modelWorkerInfo' + url;
}

//劳模信息列表
export const findVoList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.findVoList), params },
    {
      isTransformResponse: false,
    }
  );
};

//劳模信息详情
export const getById = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.getVoByDtoPc), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(modelWorker.saveOrUpdateByDTO), params },
    {
      isTransformResponse: false,
    }
  );
};

//劳模工匠删除
export const updateLogicDeleteById = params => {
  return h5Http.delete<BasicResponse>(
    { url: getApi(modelWorker.updateLogicDeleteById + '?workerId=' + params), params },
    {
      isTransformResponse: false,
    }
  );
};

export const exportFail = params => {
  return h5Http.get<any>(
    { url: getApi(modelWorker.exportFail), params, responseType: 'blob', timeout: 600 * 1000 },
    {
      isTransformResponse: false,
    }
  );
};

export const importLines = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.importLines), params },
    {
      isTransformResponse: false,
    }
  );
};

//根据身份号码解析性别+出生日期
export const analysisIdCardNumber = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.analysisIdCardNumber), params },
    {
      isTransformResponse: false,
    }
  );
};

//根据身份号码解析性别+出生日期
export const getMaxSortNumber = () => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.getMaxSortNumber) },
    {
      isTransformResponse: false,
    }
  );
};

export const failGet = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.failGet), params },
    {
      isTransformResponse: false,
    }
  );
};

export const failInfo = params => {
  return h5Http.get<BasicResponse>({ url: getApi(modelWorker.failInfo), params });
};

export const getInfos = params => {
  return h5Http.get<BasicResponse>({ url: getApi(modelWorker.getVoByDtoPc), params });
};

export const exportModelWorker = params => {
  return h5Http.get<any>(
    {
      url: getApi(modelWorker.exportModelWorker),
      params,
      responseType: 'blob',
      timeout: 1800 * 1000,
    },
    { isReturnNativeResponse: true }
  );
};

//获取劳模导入模板url获取劳模导入模板url
export const getImportTemplateURL = () => {
  return h5Http.get<BasicResponse>(
    { url: getApi(modelWorker.getImportTemplateURL), responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//查询雅安工会下所有工会
export const unionList = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: '/unionBasicData/unionPagedAll', params },
    {
      isTransformResponse: false,
    }
  );
};
