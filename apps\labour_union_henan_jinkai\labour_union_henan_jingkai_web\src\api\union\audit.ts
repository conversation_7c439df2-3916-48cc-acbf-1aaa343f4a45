import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum API {
  findList = '/applyFindVOList',
  saveApi = '/submitCompanyApply',
  updateApi = '/resubmitCompanyApply',
  auditBatch = '/auditBatch',
  cancelCompanyApply = '/cancelCompanyApply',
}

function getApi(url?: string) {
  if (!url) {
    return '/companyFront';
  }
  return '/companyFront' + url;
}

// 列表
export const list = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 审核
export const auditBatch = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.auditBatch),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 撤销申请
export const cancelCompanyApply = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.cancelCompanyApply),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
