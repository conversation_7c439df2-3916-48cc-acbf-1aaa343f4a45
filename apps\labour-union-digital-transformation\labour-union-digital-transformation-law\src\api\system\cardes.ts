import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum Cadre {
  findList = '/cadresInfo/findList',
  addCadreAndPerms = '/cadresInfo',
  updateCadreAndPerms = '/cadresInfo',
  roleBindOfficer = '/cadresInfo/roleBindOfficer',
  getUserIdsByRoleId = '/cadresInfo/getBindingValueByRoleRelation',
  delete = '/cadresInfo',
  changeState = '/cadresInfo/setEnableDisable',
}

interface Trade {
  level1: [];
  level2: [];
  level3: [];
}

function getApi(url?: string) {
  if (!url) {
    return '';
  }
  return '' + url;
}

export const getCadresList = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(Cadre.findList),
      params,
    },
    { isTransformResponse: false }
  );
};

export const addRole = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Cadre.addCadreAndPerms),
      params,
    },
    { isTransformResponse: false }
  );
};

export const updateRole = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Cadre.updateCadreAndPerms),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const deleteRole = id => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi(Cadre.delete) + `?autoId=${id}`,
    },
    {
      isTransformResponse: false,
    }
  );
};

//干部绑定关系
export const roleBindOfficer = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Cadre.roleBindOfficer),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const getUserIdsByRoleId = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(Cadre.getUserIdsByRoleId),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const roleNewsAdd = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Cadre.updateCadreAndPerms),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//工会干部查询

//管理员
export const unionNextAdminSelect = () => {
  return datacenterHttp.get<Trade>({
    url: '/unionCustomData/unionNextAdminSelect',
  });
};

export const changeState = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Cadre.changeState),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
