import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '@/utils/http/axios';

enum OBJ {
  findList = '/getAccountInfoList',
  view = '/getAccountDetail',
  saveApi = '/saveAccountAndMore',
  updateApi = '/updateAccountAndMore',
  updatePwd = '/updatePwd',
  updateAccount = '/updateAccount',
  deleteAccount = '/deleteAccount',
  getManageInfoByCompanyId = '/getManageInfoByCompanyId',
}

function getApi(url?: string) {
  if (!url) {
    return '/sysAuthAbout';
  }
  return '/sysAuthAbout' + url;
}

// view
export const getManageInfoByCompanyId = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.getManageInfoByCompanyId),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 列表
export const list = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//修改账户基础信息
export const updateAccount = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.updateAccount),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.deleteAccount) + '?openId=' + params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改密码
export const changePassword = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.updatePwd),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

/**
 * 账号的启用禁用
 */
export const accountEnableOrDisable = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: '/sysAuthAbout/accountEnableOrDisable',
      params,
    },
    { isTransformResponse: false }
  );
};

/**
 * 所属部门
 */
export const getDeptIdsByOpenId = params => {
  return datacenterHttp.get<Recordable[]>({
    url: '/sysAuthAbout/getDeptIdsByOpenId',
    params,
  });
};

/**
 * 所属角色
 */
export const getRoleListByOpenId = params => {
  return datacenterHttp.get<Recordable[]>({
    url: '/sysAuthAbout/getRoleListByOpenId',
    params,
  });
};
/**
 * 解绑列表
 */
export const getThirdPlatformList = params => {
  return datacenterHttp.get<string[]>({
    url: '/thirdLogin/getThirdPlatformList',
    params,
  });
};

export const getThirdConfigList = params => {
  return datacenterHttp.get<Recordable[]>({
    url: '/thirdLogin/getThirdConfigList',
    params,
  });
};

export const cancelBandingThirdPlatform = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: '/thirdLogin/cancelBandingThirdPlatform',
      params,
    },
    { isTransformResponse: false }
  );
};
