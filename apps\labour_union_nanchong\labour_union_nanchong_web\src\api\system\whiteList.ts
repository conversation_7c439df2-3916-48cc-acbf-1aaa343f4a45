import { datacenterHttp, h5Http } from '/@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum scene {
  findList = '/findList', //查看列表
  detail = '/queryPCDetail', //查看详情
  delete = '', //删除
  updateGuestbook = '/updateGuestbook', //公开
  saveOrUpdate = '',
  findRecordList = '/findList', //查看列表
}

function getApi(url?: string) {
  if (!url) {
    return '/whiteList';
  }
  return '/whiteList' + url;
}

function getRecordApi(url?: string) {
  if (!url) {
    return '/specialModuleRecord';
  }
  return '/specialModuleRecord' + url;
}

//查询列表
export const list = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(scene.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const view = params => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(scene.detail), params },
    {
      isTransformResponse: false,
    }
  );
};

//启用/禁用数据
export const updateGuestbook = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(scene.updateGuestbook),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//编辑
export const saveOrUpdate = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(scene.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = params => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi(scene.delete) + '?autoId=' + params,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询访问记录列表
export const recordList = params => {
  return h5Http.get<BasicResponse>(
    { url: getRecordApi(scene.findRecordList), params },
    {
      isTransformResponse: false,
    }
  );
};
