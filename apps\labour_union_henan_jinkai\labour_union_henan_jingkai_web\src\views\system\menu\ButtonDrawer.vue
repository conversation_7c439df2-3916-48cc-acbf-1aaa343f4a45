<template>
  <BasicDrawer
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    showFooter
    @ok="handleSubmit"
  >
    <div class="p-4">
      <div class="mb-4">
        <h4 class="text-lg font-medium mb-2"
          >为菜单 "{{ record?.title || record?.name }}" 分配按钮权限</h4
        >
        <p class="text-gray-500 text-sm">请选择该菜单下需要显示的按钮</p>
      </div>

      <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="text-sm text-blue-600">
          <Icon
            icon="ant-design:info-circle-outlined"
            class="mr-1"
          />
          已选择 {{ selectedButtonIds.length }} 个按钮
        </div>
      </div>
      <a-spin :spinning="loading">
        <div class="border rounded-lg p-4">
          <div class="mb-3 flex justify-between items-center">
            <span class="font-medium">可用按钮列表</span>

            <div class="space-x-2">
              <a-button
                size="small"
                @click="selectAll"
                >全选</a-button
              >
              <a-button
                size="small"
                @click="selectNone"
                >全不选</a-button
              >
            </div>
          </div>

          <a-checkbox-group
            v-model:value="selectedButtonIds"
            class="w-full"
          >
            <a-row :gutter="[16, 16]">
              <a-col
                :span="12"
                v-for="button in buttons"
                :key="button.buttonId"
              >
                <a-checkbox
                  :value="button.buttonId"
                  class="w-full flex"
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <Icon
                        v-if="button.icon"
                        :icon="button.icon"
                        class="mr-2 text-blue-500"
                      />
                      <span class="font-medium">{{ button.buttonName }}</span>
                    </div>
                    <span class="text-gray-400 text-xs">{{ button.button_code }}</span>

                    <a-tag
                      class="text-xs ml-1"
                      :color="button.createType === CreateType.SYS ? 'blue' : 'orange'"
                      v-if="button.createType"
                    >
                      {{ button.createType === CreateType.SYS ? '系统级' : '自定义' }}
                    </a-tag>
                  </div>
                </a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>

          <div
            v-if="buttons.length === 0"
            class="text-center py-8 text-gray-500"
          >
            暂无可分配的按钮
          </div>
        </div>
      </a-spin>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useDrawerInner, BasicDrawer } from '@/components/Drawer';
import { list, getDistributeButton, distributeButton } from '@/api/sys/button';
import { Icon } from '@monorepo-yysz/ui';
import { useMessage } from '@monorepo-yysz/hooks';
import { CreateType } from '@monorepo-yysz/enums';

const emit = defineEmits(['register']);

const { createMessage } = useMessage();

const record = ref<Recordable>();
const disabled = ref<boolean>(false);
const isUpdate = ref<boolean>(false);
const buttons = ref<Recordable[]>([]);
const selectedButtonIds = ref<number[]>([]);
const loading = ref<boolean>(false);

const title = computed(() => {
  return '按钮分配';
});

const [registerModal, { setDrawerProps }] = useDrawerInner(async data => {
  record.value = data.record;
  disabled.value = !!data.disabled;
  isUpdate.value = !!data.isUpdate;

  loading.value = true;

  try {
    // 获取所有按钮列表
    const { data: buttonList } = await list({ pageSize: 0 });
    buttons.value = buttonList || [];

    // 获取当前菜单已分配的按钮
    if (record.value?.menuId) {
      const { data: distributedButtons } = await getDistributeButton({
        menuId: record.value.menuId,
      });
      selectedButtonIds.value = distributedButtons?.map((item: any) => item.buttonId) || [];
    } else {
      selectedButtonIds.value = [];
    }
  } catch (error) {
    console.error('获取按钮数据失败:', error);
    createMessage.error('获取按钮数据失败');
  } finally {
    loading.value = false;
  }

  setDrawerProps({ confirmLoading: false });
});

// 全选
function selectAll() {
  selectedButtonIds.value = buttons.value.map(button => button.buttonId);
}

// 全不选
function selectNone() {
  selectedButtonIds.value = [];
}

async function handleSubmit() {
  try {
    setDrawerProps({ confirmLoading: true });

    // 调用分配按钮API
    await distributeButton({
      menuId: unref(record)?.menuId,
      buttonIdList: unref(selectedButtonIds),
    });

    createMessage.success('按钮分配成功');
  } catch (error) {
    console.error('按钮分配失败:', error);
    createMessage.error('按钮分配失败');
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>
