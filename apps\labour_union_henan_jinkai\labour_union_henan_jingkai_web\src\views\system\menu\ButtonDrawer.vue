<template>
  <BasicDrawer
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useDrawerInner, BasicDrawer } from '@/components/Drawer';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const buttons = ref<Recordable[]>([])

const title = computed(() => {
  return '按钮分配';
});

const [registerModal, { setDrawerProps }] = useDrawerInner(async data => {
  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  findVOList

  setDrawerProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setDrawerProps({ confirmLoading: true });

    emit('success', {
      values: {
        ...unref(record),
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>
