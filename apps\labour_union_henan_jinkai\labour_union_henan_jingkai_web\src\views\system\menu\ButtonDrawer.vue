<template>
  <BasicDrawer
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    showFooter
    @ok="handleSubmit"
  >
    <div class="p-4">
      <div class="mb-4">
        <h4 class="text-lg font-medium mb-2"
          >为菜单 "{{ record?.title || record?.name }}" 分配按钮权限</h4
        >
        <p class="text-gray-500 text-sm">请选择该菜单下需要显示的按钮</p>
      </div>

      <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="text-sm text-blue-600">
          <Icon
            icon="ant-design:info-circle-outlined"
            class="mr-1"
          />
          已选择 {{ selectedButtonIds.length }} 个按钮
        </div>
      </div>
      <a-spin :spinning="loading">
        <div class="border rounded-lg p-4">
          <div class="mb-3 flex justify-between items-center">
            <span class="font-medium">可用按钮列表</span>

            <div class="space-x-2">
              <a-button
                type="primary"
                size="small"
                @click="handleAddButton"
                >新增按钮</a-button
              >
              <a-button
                size="small"
                @click="selectAll"
                >全选</a-button
              >
              <a-button
                size="small"
                @click="selectNone"
                >全不选</a-button
              >
            </div>
          </div>

          <a-checkbox-group
            v-model:value="selectedButtonIds"
            class="w-full"
          >
            <a-row :gutter="[16, 16]">
              <a-col
                :span="12"
                v-for="button in buttons"
                :key="button.buttonId"
              >
                <div class="border rounded p-2 hover:bg-gray-50">
                  <div class="flex items-center justify-between">
                    <a-checkbox
                      :value="button.buttonId"
                      class="flex-1"
                    >
                      <span class="font-medium">{{ button.buttonName }}</span>
                    </a-checkbox>

                    <div class="flex items-center space-x-2">
                      <a-tag
                        class="text-xs"
                        :color="button.createType === CreateType.SYS ? 'blue' : 'orange'"
                        v-if="button.createType"
                      >
                        {{ button.createType === CreateType.SYS ? '系统级' : '自定义' }}
                      </a-tag>

                      <a-button
                        type="link"
                        size="small"
                        @click="handleEditButton(button)"
                        :disabled="button.createType === CreateType.SYS"
                        class="p-0"
                        v-if="button.createType === CreateType.CUSTOM"
                      >
                        <Icon icon="ant-design:edit-outlined" />
                      </a-button>

                      <a-button
                        type="link"
                        size="small"
                        danger
                        @click="handleDelButton(button)"
                        :disabled="button.createType === CreateType.SYS"
                        class="p-0"
                        v-if="button.createType === CreateType.CUSTOM"
                      >
                        <Icon icon="ant-design:delete-outlined" />
                      </a-button>
                    </div>
                  </div>

                  <div
                    class="text-gray-500 text-xs mt-1"
                    v-if="button.buttonCode"
                  >
                    代码: {{ button.buttonCode }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-checkbox-group>

          <div
            v-if="buttons.length === 0"
            class="text-center py-8 text-gray-500"
          >
            暂无可分配的按钮
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 按钮编辑模态框 -->
    <BasicModal
      v-model:open="editModalVisible"
      :title="editingButton?.buttonId ? '编辑按钮' : '新增按钮'"
      @ok="handleSaveButton"
      @cancel="handleCancelEdit"
      :confirmLoading="saveLoading"
    >
      <a-form
        :model="buttonForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="按钮名称"
          name="buttonName"
          :rules="[{ required: true, message: '请输入按钮名称' }]"
        >
          <a-input
            v-model:value="buttonForm.buttonName"
            placeholder="请输入按钮名称"
          />
        </a-form-item>

        <a-form-item
          label="按钮代码"
          name="buttonCode"
          :rules="[{ required: true, message: '请输入按钮代码' }]"
        >
          <a-input
            v-model:value="buttonForm.buttonCode"
            placeholder="请输入按钮代码"
            :disabled="!!editingButton?.buttonId"
          />
        </a-form-item>
      </a-form>
    </BasicModal>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, unref, computed, reactive } from 'vue';
import { useDrawerInner, BasicDrawer } from '@/components/Drawer';
import { BasicModal } from '@/components/Modal';
import {
  list,
  getDistributeButton,
  deleteLine,
  distributeButton,
  saveApi,
  updateApi,
} from '@/api/sys/button';
import { Icon } from '@monorepo-yysz/ui';
import { useMessage } from '@monorepo-yysz/hooks';
import { CreateType } from '@monorepo-yysz/enums';
import { sortBy } from 'lodash-es';

const emit = defineEmits(['register']);

const { createMessage, createConfirm, createSuccessModal, createErrorModal } = useMessage();

const record = ref<Recordable>();
const disabled = ref<boolean>(false);
const isUpdate = ref<boolean>(false);
const buttons = ref<Recordable[]>([]);
const selectedButtonIds = ref<number[]>([]);
const loading = ref<boolean>(false);

// 按钮编辑相关状态
const editModalVisible = ref<boolean>(false);
const editingButton = ref<Recordable | null>(null);
const saveLoading = ref<boolean>(false);
const buttonForm = reactive({
  buttonName: '',
  buttonCode: '',
});

const title = computed(() => {
  return '按钮分配';
});

const [registerModal, { setDrawerProps }] = useDrawerInner(async data => {
  record.value = data.record;
  disabled.value = !!data.disabled;
  isUpdate.value = !!data.isUpdate;

  loading.value = true;

  try {
    // 获取所有按钮列表
    await refreshButtonList();
    // 获取当前菜单已分配的按钮
    if (record.value?.menuId) {
      const { data: distributedButtons } = await getDistributeButton({
        menuId: record.value.menuId,
      });
      selectedButtonIds.value = distributedButtons?.map((item: any) => item.buttonId) || [];
    } else {
      selectedButtonIds.value = [];
    }
  } catch (error) {
    console.error('获取按钮数据失败:', error);
    createMessage.error('获取按钮数据失败');
  } finally {
    loading.value = false;
  }

  setDrawerProps({ confirmLoading: false });
});

// 全选
function selectAll() {
  selectedButtonIds.value = buttons.value.map(button => button.buttonId);
}

// 全不选
function selectNone() {
  selectedButtonIds.value = [];
}

// 新增按钮
function handleAddButton() {
  editingButton.value = null;
  buttonForm.buttonName = '';
  buttonForm.buttonCode = '';
  editModalVisible.value = true;
}

// 编辑按钮
function handleEditButton(button: Recordable) {
  if (button.createType === CreateType.SYS) {
    createMessage.warning('系统级按钮不允许编辑');
    return;
  }

  editingButton.value = button;
  buttonForm.buttonName = button.buttonName || '';
  buttonForm.buttonCode = button.buttonCode || '';
  editModalVisible.value = true;
}

// 删除按钮 （逻辑删除）
function handleDelButton(button: Recordable) {
  if (button.createType === CreateType.SYS) {
    createMessage.warning('系统级按钮不允许编辑');
    return;
  }

  createConfirm({
    iconType: 'warning',
    content: `请确定删除${button.buttonName || ''}`,
    onOk: () => {
      deleteLine({
        buttonIdList: [button.buttonId],
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          refreshButtonList();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

// 取消编辑
function handleCancelEdit() {
  editModalVisible.value = false;
  editingButton.value = null;
}

// 保存按钮
async function handleSaveButton() {
  if (!buttonForm.buttonName.trim()) {
    createMessage.error('请输入按钮名称');
    return;
  }

  if (!buttonForm.buttonCode.trim()) {
    createMessage.error('请输入按钮代码');
    return;
  }

  saveLoading.value = true;

  try {
    const params = {
      buttonName: buttonForm.buttonName.trim(),
      buttonCode: buttonForm.buttonCode.trim(),
      createType: CreateType.CUSTOM, // 自定义按钮
    };

    if (editingButton.value?.buttonId) {
      // 编辑模式
      await updateApi({
        ...params,
        buttonId: editingButton.value.buttonId,
      });
      createMessage.success('按钮修改成功');
    } else {
      // 新增模式
      await saveApi(params);
      createMessage.success('按钮新增成功');
    }

    editModalVisible.value = false;
    editingButton.value = null;

    // 刷新按钮列表
    await refreshButtonList();
  } catch (error) {
    console.error('保存按钮失败:', error);
    createMessage.error('保存按钮失败');
  } finally {
    saveLoading.value = false;
  }
}

// 刷新按钮列表
async function refreshButtonList() {
  try {
    const { data: buttonList } = await list({ pageSize: 0 });
    buttons.value = sortBy(buttonList || [], 'buttonId');
  } catch (error) {
    console.error('刷新按钮列表失败:', error);
  }
}

async function handleSubmit() {
  try {
    setDrawerProps({ confirmLoading: true });

    // 调用分配按钮API
    await distributeButton({
      menuId: unref(record)?.menuId,
      buttonIdList: unref(selectedButtonIds),
    });

    createMessage.success('按钮分配成功');
  } catch (error) {
    console.error('按钮分配失败:', error);
    createMessage.error('按钮分配失败');
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>
