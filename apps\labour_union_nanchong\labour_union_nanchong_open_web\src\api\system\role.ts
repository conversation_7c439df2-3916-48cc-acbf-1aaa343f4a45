import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum API {
  findList = '/simpleFindVoList',
  view = '',
  saveOrUpdate = '',
  saveApi = '/',
  updateApi = '/',
}

function getApi(url?: string) {
  if (!url) {
    return '/roleInfo';
  }
  return '/roleInfo' + url;
}

// 列表
export const list = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增修改
export const saveOrUpdate = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
