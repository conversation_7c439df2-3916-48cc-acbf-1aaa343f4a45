import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '@/utils/http/axios';

enum LABEL {
  findVoList = '/findVoList',
  changeState = '/changeState',
  changeValue = '/changeValue',
}
function getApi(url?: string) {
  if (!url) {
    return '/everydaySignRule';
  }
  return '/everydaySignRule' + url;
}
// 列表
export const findVoList = (params: any) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(LABEL.findVoList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 更改补签规则启用状态
export const changeState = (params: any) => {
  return datacenterHttp.post<BasicResponse>(
    { url: getApi(LABEL.changeState), params },
    {
      isTransformResponse: false,
    }
  );
};

// 更改补签规则值
export const changeValue = (params: any) => {
  return datacenterHttp.post<BasicResponse>(
    { url: getApi(LABEL.changeValue), params },
    {
      isTransformResponse: false,
    }
  );
};
