import { openHttp, h5Http, datacenterHttp, manageHttp } from '/@/utils/http/axios';

// 查询服务模块数据
export const pvCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/pv-count',
    params,
  });
};
// 查询服务模块详情--曲线数据
export const moduleAnalysis = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/moduleAnalysis',
    params,
  });
};

// 查询用户数据
export const unionAndUserCount = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/kbByArea',
    params,
  });
};
// 查询平台用户
export const accessCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/accessCount',
    params,
  });
};

//视频专区
// 查询视频专区
export const videoZoneCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/videoZoneCount',
    params,
  });
};
// 查询视频专区详情--半年
export const videoZoneCountHalf = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/videoZoneCountHalf',
    params,
  });
};
// 查询视频专区详情--排行榜
export const videoZoneList = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/videoZoneList',
    params,
  });
};

//工友之声
// 查询工友之声
export const voiceCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/voiceCount',
    params,
  });
};
// 查询工友之声详情--类型
export const voiceTypeCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/voiceTypeCount',
    params,
  });
};
// 查询工友之声详情--区域
export const voiceCountByArea = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/voiceCountByArea',
    params,
  });
};

// 查询各区域访问量
export const areaCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/area-count',
    params,
  });
};

// 查询各用户画像
export const userDetails = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/userThatPersonStatisticsDetails',
    params,
  });
};

//用户访问量
// 查询用户访问分析
export const getSummary = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/getSummary',
    params,
  });
};

// 查询用户访问分析月份数据
export const summaryAndMonth = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/getSummaryAndMonth',
    params,
  });
};
// 查询各区域访问量折线图
export const chartsData = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/chartsData',
    params,
  });
};

// 查询待办和办结数据
export const moderationCounts = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/queryModerationCounts',
    params,
  });
};
// 查询用户行为数据分析--画像
export const userConductCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/userConductCount',
    params,
  });
};
// 查询用户行为数据分析--标签
export const userLabel = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/userLabel',
    params,
  });
};
// 查询用户行为详情--曲线数据
export const variousLabels = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/variousLabels',
    params,
  });
};
// 查询用户行为详情--曲线数据
export const variousLabelsByLabelCode = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/variousLabelsByLabelCode',
    params,
  });
};
// 查询待办和办结数据
export const applyCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/applyCount',
    params,
  });
};
// 查询待办和办结数据
export const doneCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/doneCount',
    params,
  });
};
// 查询各区县业务结办数据
export const ncUnionCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/ncUnionCount',
    params,
  });
};
// 查询积分本年增长分析
export const halfYearSummary = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/halfYearSummary',
    params,
  });
};
// 查询积分增长来源分析
export const addIntegralResource = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/addIntegralResource',
    params,
  });
};
// 查询各区域积分数分析
export const dataSummaryByAreaName = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/dataSummaryByAreaName',
    params,
  });
};
// 查询积分前50排行
export const getIntegralTop50Rank = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/getIntegralTop50Rank',
    params,
  });
};
// 查询积分分段统计
export const integralSegmentationCount = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/integralSegmentationCount',
    params,
  });
};
// 查询积分分段个区域分析
export const integralAreaNumber = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/areaNumber',
    params,
  });
};
// 查询积分
export const integralSummary = (params?: Recordable) => {
  return datacenterHttp.get<Recordable>({
    url: '/customIntegral/dataSummary',
    params,
  });
};
// 查询阅读量告警分析
export const newsReadCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/newsReadCountAlarmAnalysis',
    params,
  });
};

//单身联谊
// 查询单身联谊
export const singleCount = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/singleCount',
    params,
  });
};
// 查询单身联谊--学历
export const singleDataAnalysis = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/singleDataAnalysis',
    params,
  });
};
// 查询单身联谊--性别
export const genderByArea = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/genderByArea',
    params,
  });
};

//思想引领
// 查询思想引领全市
export const unionNewsStatistics = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/unionNewsStatistics',
    params,
  });
};
// 查询思想引领区域
export const unionRegionAnalysis = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/unionRegionAnalysis',
    params,
  });
};
// 查询思想引领--阅读量告警分析
export const newsReadCountAlarmAnalysis = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/newsReadCountAlarmAnalysis',
    params,
  });
};
// 查询思想引领统计
export const newsStatisticsPc = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/newsStatisticsPc',
    params,
  });
};
// 查询区域会员行为分析
export const areaUserBehaviorPc = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/areaUserBehaviorPc',
    params,
  });
};
// 新闻排行榜
export const getNewsRankingList = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/getNewsRankingList',
    params,
  });
};
// 新闻栏目分析
export const newsByCategory = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/newsByCategory',
    params,
  });
};
// 新闻类型分析
export const newsTypeAnalyse = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/newsTypeAnalyse',
    params,
  });
};
// 思想引领详情--用户行为分析
export const unionGetRegion = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/unionGetRegion',
    params,
  });
};
// 思想引领详情--栏目信息
export const unionGetRegionCategory = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/unionGetRegionCategory',
    params,
  });
};

//活动接口
// 查询半年活动数据统计
export const activityInfo = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/activityInfo/halfYear/index',
    params,
  });
};

//普惠资源接口

// 首页--普惠资源统计
export const merchantType = (params?: Recordable) => {
  return openHttp.get<Recordable>({
    url: '/openDataSummary/merchantType',
    params,
  });
};

// ====首页顶部数据统计-详情====

//会员类型统计
export const userTypeCount = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/userTypeCount',
    params,
  });
};
//区域会员数据统计
export const userCountByArea = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/userCountByArea',
    params,
  });
};
//工会所在单位性质
export const unionUnitType = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/unionUnitType',
    params,
  });
};
//所属行业
export const unionIndustryType = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/unionIndustryType',
    params,
  });
};
//工会干部占比统计
export const cadreProportion = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/cadreProportion',
    params,
  });
};
//工会干部类型
export const cadreType = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/cadreType',
    params,
  });
};
//会员认证，用户转化
export const kbAllData = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/kbAllData',
    params,
  });
};
//活跃用户
export const activityCountByArea = (params?: Recordable) => {
  return manageHttp.get<Recordable>({
    url: '/unionAndUserCount/activityCountByArea',
    params,
  });
};

// ====end====
