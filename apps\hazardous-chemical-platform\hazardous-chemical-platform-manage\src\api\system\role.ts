import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '@/utils/http/axios';

enum OBJ {
  findList = '/findRoleInfoList',
  view = '/getRoleInfo',
  getRoleListByAccount = '/getRoleListByAccount',
  save = '/saveRoleInfo',
  update = '/updateRoleInfo',
  permisstion = '/findRolePermissionList',
  roleEnableOrDisable = '/roleEnableOrDisable',
  deleteRoleInfo = '/deleteRoleInfo',
  findRolePermissionList = '/findRolePermissionList',
}

function getApi(url?: string) {
  if (!url) {
    return '/sysAuthAbout';
  }
  return '/sysAuthAbout' + url;
}

//列表
export const list = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增
export const save = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.save),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//修改
export const update = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.update),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取指定账号的角色信息
export const getRoleListByAccount = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.getRoleListByAccount),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.deleteRoleInfo),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 角色启用和禁用
export const roleEnableOrDisable = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.roleEnableOrDisable),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const roleList = (params: { pageSize: number; userId: string }) => {
  return datacenterHttp.post<Recordable[]>({
    url: OBJ.findList,
    params,
  });
};

export const permisstion = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.permisstion),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const findRolePermissionList = params => {
  return datacenterHttp.get<Recordable>({
    url: getApi(OBJ.findRolePermissionList),
    params,
  });
};
