import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum Role {
  findList = '/sysAuthAbout/findRoleInfoList',
  addRoleAndPerms = '/sysAuthAbout/saveRoleInfo',
  updateRoleAndPerms = '/sysAuthAbout/updateRoleInfo',
  distributeUserByRole = '/dataCenterBusiness/distributeRole',
  roleBindOfficer = '/dataCenterBusiness/roleBindOfficer',
  getUserIdsByRoleId = '/sysAuthAbout/getRoleInfo',
  delete = '/sysAuthAbout/deleteRoleInfo',
  roleEnableOrDisable = '/sysAuthAbout/roleEnableOrDisable',
  findRolePermissionList = '/sysAuthAbout/findRolePermissionList',
}

function getApi(url?: string) {
  if (!url) {
    return '';
  }
  return '' + url;
}

export const getRoleList = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(Role.findList),
      params,
    },
    { isTransformResponse: false }
  );
};

export const addRole = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.addRoleAndPerms),
      params,
    },
    { isTransformResponse: false }
  );
};

export const updateRole = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.updateRoleAndPerms),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const deleteRole = id => {
  return datacenterHttp.delete<BasicResponse>(
    {
      url: getApi(Role.delete) + `?autoId=${id}`,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 绑定关系
export const distributeUserByRole = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.distributeUserByRole),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 干部绑定关系
export const roleBindOfficer = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.roleBindOfficer),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const getUserIdsByRoleId = params => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(Role.getUserIdsByRoleId),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const roleNewsAdd = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.updateRoleAndPerms),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 角色启用和禁用
export const roleEnableOrDisable = params => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: Role.roleEnableOrDisable,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const findRolePermissionList = params => {
  return datacenterHttp.get<Recordable>({
    url: Role.findRolePermissionList,
    params,
  });
};
