import { adminHttp, datacenterHttp } from '@/utils/http/axios';
import { getMenuListResultModel } from './model/menuModel';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  GetMenuList = '/sysAuthAbout/getMenuTreeListCurrent',
  remove = '/menuInfo',
  menuSaveByDTO = '/menu/menuSaveByDTO',
  menuUpdateByDTO = '/menu/menuUpdateByDTO',
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList = () => {
  return datacenterHttp.get<getMenuListResultModel>({ url: Api.GetMenuList });
};

export const deleteLine = id => {
  return adminHttp.delete<BasicResponse>(
    {
      url: Api.remove + '?autoId=' + id,
    },
    { isTransformResponse: false }
  );
};

// 新增
export const menuSaveByDTO = (params: Recordable) => {
  return adminHttp.post<BasicResponse>(
    {
      url: Api.menuSaveByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};

// 修改
export const menuUpdateByDTO = (params: Recordable) => {
  return adminHttp.post<BasicResponse>(
    {
      url: Api.menuUpdateByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};
