import { BasicResponse } from '@monorepo-yysz/types';
import { datacenterHttp } from '/@/utils/http/axios';

enum API {
  findList = '/findVOList',
  getDistributeButton = '/getDistributeButton',
  distributeButton = '/distributeButton',
  saveApi = '/addButton',
  updateApi = '/updateButton',
  deleteButton = '/deleteButton',
}

function getApi(url?: string) {
  if (!url) {
    return '/button';
  }
  return '/button' + url;
}

// 列表
export const list = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// distributeButton
export const distributeButton = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.distributeButton),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// getDistributeButton
export const getDistributeButton = (params: Recordable) => {
  return datacenterHttp.get<BasicResponse>(
    {
      url: getApi(API.getDistributeButton),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (params: Recordable) => {
  return datacenterHttp.post<BasicResponse>(
    {
      url: getApi(API.deleteButton),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
